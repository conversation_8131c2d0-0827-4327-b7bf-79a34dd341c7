<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.jeecg.modules.system.mapper.SysTenantMapper">

    <!--获取租户回收站的数据假删除-->
    <select id="getRecycleBinPageList" resultType="org.jeecg.modules.system.entity.SysTenant">
        SELECT id,name,company_logo,house_number,status FROM sys_tenant
        WHERE
        del_flag = 1
        <if test="sysTenant.name!='' and sysTenant.name!=null">
            <bind name="name" value="'%'+sysTenant.name+'%'"/>
            AND name like #{name}
        </if>
        <if test="sysTenant.houseNumber!='' and sysTenant.houseNumber!=null">
            <bind name="houseNumber" value="'%'+sysTenant.houseNumber+'%'"/>
            AND house_number like #{houseNumber}
        </if>
    </select>

    <!--彻底删除租户信息-->
    <delete id="deleteByTenantId">
        DELETE FROM sys_tenant
        WHERE
        del_flag = 1
        AND id in
        <foreach collection="tenantIds" index="index" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
    <delete id="removeByIdList">
        DELETE FROM sys_tenant
        WHERE
            id in
        <foreach collection="idList" index="index" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>

    </delete>

    <!--租户还原-->
    <update id="revertTenantLogic">
        UPDATE sys_tenant set del_flag = 0
        WHERE
        del_flag = 1
        AND id in
        <foreach collection="tenantIds" index="index" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

    <!-- 用于统计 租户产品包的人员数量 -->
    <select id="queryTenantPackUserCount" resultType="org.jeecg.modules.system.vo.tenant.TenantPackUserCount">
        SELECT pack_code, count(*) as user_count FROM sys_tenant_pack a
        join sys_tenant_pack_user b on a.id = b.pack_id
        join sys_user_tenant sut on a.tenant_id = sut.tenant_id and b.user_id = sut.user_id and sut.status = 1
        where a.tenant_id = #{tenantId}
        and a.pack_code in ('superAdmin', 'accountAdmin', 'appAdmin')
        and b.status = 1
        group by a.pack_code
    </select>


    <!-- 查询人员的产品包编码 -->
    <select id="queryUserPackCode" resultType="java.lang.String">
        SELECT a.pack_code FROM sys_tenant_pack a
        join sys_tenant_pack_user b on a.id = b.pack_id
        where a.tenant_id = #{tenantId}
        and b.user_id = #{userId}
        and b.status = 1
    </select>

    <!-- 查询人员是不是租户产品包的 超级管理员 -->
    <select id="querySuperAdminCount" resultType="java.lang.Integer">
        SELECT count(*) FROM sys_tenant_pack a
        join sys_tenant_pack_user b on a.id = b.pack_id
        where a.pack_code = 'superAdmin'
        and a.tenant_id = #{tenantId}
        and b.user_id = #{userId}
        and b.status = 1
    </select>

    <!-- 查询产品包关联的用户列表 -->
    <select id="queryPackUserList" resultType="org.jeecg.modules.system.vo.tenant.TenantPackUser">
        SELECT c.id, c.username, c.realname, c.phone, c.avatar, a.pack_name, a.id as pack_id  FROM sys_user c
        join sys_tenant_pack_user b on c.id = b.user_id
        join sys_tenant_pack a on a.id = b.pack_id
        join sys_user_tenant sut on a.tenant_id = sut.tenant_id and b.user_id = sut.user_id and sut.status = 1
        where c.status = 1
        and c.del_flag = 0
        and b.status = #{packUserStatus}
        and a.tenant_id = #{tenantId}
        <if test="packId!='' and packId!=null">
            and a.id = #{packId}
        </if>
    </select>


    <!-- 根据用户ID 查询部门 -->
    <select id="queryUserDepartList" resultType="org.jeecg.modules.system.vo.tenant.UserDepart">
        SELECT c.id as user_id,a.depart_name  FROM sys_user c
        join sys_user_depart b on c.id = b.user_id
        join sys_depart a on a.id = b.dep_id
        where c.status = 1 and c.del_flag = 0
        and c.id in
        <foreach collection="userIdList" index="index" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>

    <!-- 根据用户ID 查询职位 -->
    <select id="queryUserPositionList" resultType="org.jeecg.modules.system.vo.tenant.UserPosition">
        SELECT c.id as user_id, name as position_name FROM sys_user c
        join sys_user_position b on c.id = b.user_id
        join sys_position a on a.id = b.position_id
        where c.status = 1 and c.del_flag = 0
        and c.id in
        <foreach collection="userIdList" index="index" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>

    <!--获取租户产品包用户列表-->
    <select id="queryTenantPackUserList" resultType="org.jeecg.modules.system.vo.tenant.TenantPackUser">
        SELECT c.id, c.username, c.realname, c.phone, c.avatar, a.pack_name, a.id as pack_id  FROM sys_user c
        join sys_tenant_pack_user b on c.id = b.user_id
        join sys_tenant_pack a on a.id = b.pack_id
        where c.status = 1
        and c.del_flag = 0
        and b.status = #{status}
        and a.tenant_id = #{tenantId}
        <if test="packId!='' and packId!=null">
            and a.id = #{packId}
        </if>
    </select>

    <!--查看是否已经申请过了超级管理员-->
    <select id="getApplySuperAdminCount" resultType="java.lang.Long">
        SELECT count(*) FROM sys_tenant_pack a
        join sys_tenant_pack_user b on a.id = b.pack_id
        where a.pack_code = 'superAdmin'
        and a.tenant_id = #{tenantId}
        and b.user_id = #{userId}
    </select>

    <!--根据用户id获取租户信息-->
    <select id="getTenantListByUserId" resultType="org.jeecg.modules.system.entity.SysTenant">
        SELECT st.id,st.name FROM sys_tenant st
        LEFT JOIN sys_user_tenant sut on st.id= sut.tenant_id and st.status = 1 and sut.status='1'
        WHERE sut.user_id = #{userId}
    </select>
    <select id="getNameClick" resultType="java.lang.String">
            SELECT click.wh_num FROM wh_click click
                        WHERE click.pid=#{id}
    </select>
    <select id="selectTenantDetailsList" resultType="org.jeecg.modules.system.dto.SysTenantDetailsVO">
        SELECT
        id,
        name,
        company_address,
        intro,
        work_place,
        phone,
        reg_time,
        legal_person,
        score,
        wechat_logo,
        bus_type
        FROM sys_tenant
        WHERE del_flag = 0
        <if test="tenantType != null and tenantType != ''">
            AND EXISTS (
            SELECT 1
            FROM (
            SELECT TRIM(SUBSTRING_INDEX(SUBSTRING_INDEX(#{tenantType}, ',', numbers.n), ',', -1)) AS type_value
            FROM (
            SELECT 1 AS n UNION ALL SELECT 2 UNION ALL SELECT 3 UNION ALL SELECT 4 UNION ALL SELECT 5
            ) numbers
            ) AS types
            WHERE FIND_IN_SET(type_value, bus_type)
            )
        </if>
        <if test="excludeId != null">
            AND id != #{excludeId}
        </if>
    </select>

    <select id="selectRandomTenant" resultType="org.jeecg.modules.system.dto.SysTenantDetailsVO">
        SELECT
        id,
        name,
        company_address,
        intro,
        work_place,
        phone,
        reg_time,
        legal_person,
        score,
        wechat_logo,
        bus_type
        FROM sys_tenant
        WHERE del_flag = 0
        <if test="tenantType != null and tenantType != ''">
            AND EXISTS (
            SELECT 1
            FROM (
            SELECT TRIM(SUBSTRING_INDEX(SUBSTRING_INDEX(#{tenantType}, ',', numbers.n), ',', -1)) AS type_value
            FROM (
            SELECT 1 AS n UNION ALL SELECT 2 UNION ALL SELECT 3 UNION ALL SELECT 4 UNION ALL SELECT 5
            ) numbers
            ) AS types
            WHERE FIND_IN_SET(type_value, bus_type)
            )
        </if>
        ORDER BY RAND()
        LIMIT 1
    </select>

    <insert id="saveWhClick">
        INSERT INTO wh_click (id, wh_num, pid)
        VALUES (#{id}, #{num}, #{pid})
    </insert>

    <!-- 修改点击数 -->
    <update id="updateWhClick">
        UPDATE wh_click
        SET wh_num = #{clicksNum}, update_time = NOW()
        WHERE pid = #{pid}
    </update>

    <!-- 插入公司信息与点击量关联 -->
    <insert id="insertFirmInformation">
        INSERT INTO wh_firm_information (name, company_id, clicks_id, create_time)
        VALUES (#{name}, #{companyId}, #{clicksId}, NOW())
    </insert>

    <!-- 根据公司ID获取点击ID -->
    <select id="getClicksIdByCompanyId" resultType="java.lang.String">
        SELECT wh_num FROM wh_click
        WHERE pid = #{pid}
        LIMIT 1
    </select>

    <!-- 获取公司目录（只返回id和name） -->
    <select id="getCompanyDirectory" resultType="org.jeecg.modules.system.dto.SysTenantVO">
        SELECT
        t.id AS id,
        t.name AS name
        FROM sys_tenant t
        <where>
            t.del_flag = 0
            AND t.status = 1

            <if test="dto.name != null and dto.name != ''">
                AND t.name LIKE CONCAT('%', #{dto.name}, '%')
            </if>

            <if test="dto.tenantId != null and dto.tenantId != ''">
                AND t.id = #{dto.tenantId}
            </if>

            <if test="dto.tenantIds != null and dto.tenantIds.size() > 0">
                AND t.id IN
                <foreach collection="dto.tenantIds" item="tid" open="(" separator="," close=")">
                    #{tid}
                </foreach>
            </if>

            <if test="dto.tenantList != null and dto.tenantList.size() > 0">
                AND t.id IN
                <foreach collection="dto.tenantList" item="tid" open="(" separator="," close=")">
                    #{tid}
                </foreach>
            </if>
        </where>
    </select>
    <select id="queryList" resultType="org.jeecg.modules.system.dto.SysTenantPortalVO">
        SELECT
            t.id AS id,
            t.company_logo AS companyLogo,
            t.name AS name,
            t.website AS website,
            t.reg_time AS regTime,
            IFNULL(crh.today_clicks, 0) AS clickNum
        FROM sys_tenant t
        LEFT JOIN (
            SELECT
                tenant_id,
                SUM(click_num) AS today_clicks
            FROM click_report_hourly
            WHERE stat_date = CURDATE()
                AND config_type = 0
            GROUP BY tenant_id
        ) crh ON crh.tenant_id = t.id
        <where>
            t.del_flag = 0 AND t.is_show = 1
            <if test="dto.tenantName != null and dto.tenantName != ''">
                AND t.name LIKE CONCAT('%', #{dto.tenantName}, '%')

            </if>
        </where>
        ORDER BY
        t.sort_order DESC

    </select>
    <select id="queryPageList" resultType="org.jeecg.modules.system.entity.SysTenant">
        SELECT
        t.id AS id,
        t.company_address AS companyAddress,
        t.company_logo AS companyLogo,
        t.score AS score,
        t.wechat_logo AS wechatLogo,
        t.bus_type AS busType,
        t.is_top AS isTop,
        t.name AS name,
        t.create_by AS createBy,
        t.create_time AS createTime,
        t.begin_date AS beginDate,
        t.end_date AS endDate,
        t.status AS status,
        t.sort_order AS sortOrder,
        t.is_show AS isShow,
        t.trade AS trade,
        t.company_size AS companySize,
        t.ip_address AS ipAddress,
        t.house_number AS houseNumber,
        t.work_place AS workPlace,
        t.phone AS phone,
        t.website AS website,
        t.email AS email,
        t.credit_code AS creditCode,
        t.registered_capital AS registeredCapital,
        t.secondary_domain AS secondaryDomain,
        t.login_bkgd_img AS loginBkgdImg,
        t.position AS position,
        t.tenant_type AS tenantType,
        t.department AS department,
        t.del_flag AS delFlag,
        t.update_by AS updateBy,
        t.update_time AS updateTime,
        t.apply_status AS applyStatus,
        t.reg_time AS regTime,
        t.legal_person AS legalPerson,
        IFNULL(wh.wh_num, 0) AS clickNum
        FROM sys_tenant t
        LEFT JOIN wh_click wh ON wh.pid = t.id
        <where>
            t.del_flag = 0
            <if test="dto.name != null and dto.name != ''">
                AND t.name LIKE CONCAT('%', #{dto.name}, '%')
            </if>
            <if test="dto.id != null">
                AND t.id = #{dto.id}
            </if>
            <if test="dto.tenantId != null">
                AND t.id = #{dto.tenantId}
            </if>
        </where>
        ORDER BY
        <choose>
            <when test="dto.orderBy != null and dto.orderBy != ''">
                ${dto.orderBy}
            </when>
            <otherwise>
                t.sort_order DESC
            </otherwise>
        </choose>
    </select>


</mapper>