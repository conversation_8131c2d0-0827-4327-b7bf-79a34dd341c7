<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.jeecg.modules.corp.mapper.ClickReportHourlyMapper">

    <!-- 根据日期范围获取点击统计数据 -->
    <select id="getClickStatsByDateRange" resultType="java.util.HashMap">
        SELECT
            stat_date,
            SUM(click_num) as total_uv,
            SUM(click_pv) as total_pv
        FROM
            click_report_hourly
        WHERE
            1=1
            <if test="params.startDate != null">
                AND stat_date &gt;= #{params.startDate}
            </if>
            <if test="params.endDate != null">
                AND stat_date &lt;= #{params.endDate}
            </if>
            <if test="params.configType != null">
                AND config_type = #{params.configType}
            </if>
            <if test="params.tenantId != null">
                AND tenant_id = #{params.tenantId}
            </if>
        GROUP BY
            stat_date
        ORDER BY
            stat_date ASC
    </select>

    <!-- 获取今日点击数统计 -->
    <select id="getTodayClickCount" resultType="java.lang.Integer">
        SELECT
            IFNULL(SUM(click_num), 0) as total_clicks
        FROM
            click_report_hourly
        WHERE
            stat_date = CURDATE()
            AND config_type = 0
            <if test="tenantId != null and tenantId != 0">
                AND tenant_id = #{tenantId}
            </if>
    </select>

</mapper>