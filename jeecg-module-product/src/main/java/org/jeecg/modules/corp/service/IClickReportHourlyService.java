package org.jeecg.modules.corp.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import org.jeecg.modules.corp.entity.ClickReportHourly;

import java.util.List;
import java.util.Map;

/**
 * @Description: 按小时统计点击报表Service
 * @Author: jeecg-boot
 * @Date: 2024-05-16
 */
public interface IClickReportHourlyService extends IService<ClickReportHourly> {

    /**
     * 根据日期范围获取点击统计数据
     *
     * @param params 参数Map，包含startDate, endDate, configType, tenantId等参数
     * @return 统计数据列表
     */
    List<Map<String, Object>> getClickStatsByDateRange(Map<String, Object> params);

    /**
     * 新增按小时统计点击报表
     *
     * @param dto 参数
     */
    ClickReportHourly add(ClickReportHourly dto);

    /**
     * 修改按小时统计点击报表
     *
     * @param dto 参数
     */
    ClickReportHourly edit(ClickReportHourly dto);

    /**
     * 删除按小时统计点击报表
     *
     * @param id 主键
     */
    void deleteById(String id);

    /**
     * 分页查询按小时统计点击报表
     *
     * @param page 分页参数
     * @param dto 查询条件
     * @return 分页结果
     */
    IPage<ClickReportHourly> findPage(IPage<ClickReportHourly> page, ClickReportHourly dto);

    /**
     * 获取今日点击数统计
     *
     * @param tenantId 租户ID，如果为0则统计所有租户
     * @return 今日点击数总和
     */
    Integer getTodayClickCount(Integer tenantId);
}


