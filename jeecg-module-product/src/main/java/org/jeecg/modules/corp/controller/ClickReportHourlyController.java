package org.jeecg.modules.corp.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.shiro.SecurityUtils;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.system.vo.LoginUser;
import org.jeecg.common.util.oConvertUtils;
import org.jeecg.modules.corp.entity.ClickReportHourly;
import org.jeecg.modules.corp.service.IClickReportHourlyService;
import org.jeecg.modules.corp.vo.TodayClickCountVo;
import org.jeecg.modules.system.entity.SysUser;
import org.jeecg.modules.system.service.ISysUserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

/**
* <p>
 * 按小时统计点击报表前端控制器
 * </p>
*
* <AUTHOR>
* @since 2025-05-12
*/
@Api(tags = "按小时统计点击报表")
@RestController
@Slf4j
@RequestMapping("click-report-hourly")
public class ClickReportHourlyController {
    @Autowired
    private IClickReportHourlyService iClickReportHourlyService;

    @Autowired
    private ISysUserService sysUserService;

//    @ApiOperation("新增按小时统计点击报表")
//    @PostMapping("/add")
//    public DataResult<ClickReportHourly> addClickReportHourly(@Valid @RequestBody ClickReportHourly dto) {
//        return SysHttpResult.SUCCESS.getDataResult(iClickReportHourlyService.add(dto));
//    }
//
//    @ApiOperation("修改按小时统计点击报表")
//    @PutMapping("/edit/{id}")
//    @ApiImplicitParam(value = "按小时统计点击报表id",name = "id",dataTypeClass = String.class)
//    public DataResult<ClickReportHourly> editClickReportHourly(@PathVariable String id, @Valid @RequestBody ClickReportHourly dto) {
//        dto.setId(id);
//        return SysHttpResult.SUCCESS.getDataResult(iClickReportHourlyService.edit(dto));
//    }
//
//
//    @ApiOperation("删除按小时统计点击报表")
//    @DeleteMapping("/delete/{id}")
//    @ApiImplicitParam(value = "按小时统计点击报表id",name = "id",dataTypeClass = String.class)
//    public DataResult deleteClickReportHourly(@PathVariable String id) {
//        iClickReportHourlyService.deleteById(id);
//        return SysHttpResult.SUCCESS.getDataResult();
//    }





    /**
     * 获取今日点击数统计
     *
     * @return 今日点击数统计结果
     */
    @ApiOperation("获取今日点击数统计")
    @GetMapping("/getTodayClickCount")
    public Result<TodayClickCountVo> getTodayClickCount() {
        try {
            // 获取当前登录用户
            LoginUser loginUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
            if (loginUser == null) {
                return Result.error("用户未登录");
            }

            // 通过用户名获取完整的用户信息（包含bindTenant）
            SysUser sysUser = sysUserService.getUserByName(loginUser.getUsername());
            Integer queryTenantId;
            if (sysUser != null && oConvertUtils.isNotEmpty(sysUser.getBindTenant())) {
                queryTenantId = Integer.valueOf(sysUser.getBindTenant());
            } else {
                // 如果用户没有绑定租户，则设置为0（查询所有租户）
                queryTenantId = 0;
            }

            // 调用Service获取今日点击数
            Integer todayClickCount = iClickReportHourlyService.getTodayClickCount(queryTenantId);

            // 构造返回结果
            TodayClickCountVo result = new TodayClickCountVo(todayClickCount, queryTenantId);

            return Result.ok(result);

        } catch (Exception e) {
            log.error("获取今日点击数统计失败", e);
            return Result.error("获取今日点击数统计失败: " + e.getMessage());
        }
    }

}