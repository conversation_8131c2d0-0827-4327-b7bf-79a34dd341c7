package org.jeecg.modules.corp.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.jeecg.modules.corp.entity.ClickReportHourly;

import java.util.List;
import java.util.Map;

/**
 * @Description: 按小时统计点击报表Mapper
 * @Author: jeecg-boot
 * @Date: 2024-05-16
 */
@Mapper
public interface ClickReportHourlyMapper extends BaseMapper<ClickReportHourly> {

    /**
     * 根据日期范围获取点击统计数据
     *
     * @param params 参数Map，包含startDate, endDate, configType, tenantId等参数
     * @return 统计数据列表
     */
    List<Map<String, Object>> getClickStatsByDateRange(@Param("params") Map<String, Object> params);

    /**
     * 获取今日点击数统计
     *
     * @param tenantId 租户ID，如果为0则统计所有租户
     * @return 今日点击数总和
     */
    Integer getTodayClickCount(@Param("tenantId") Integer tenantId);
}