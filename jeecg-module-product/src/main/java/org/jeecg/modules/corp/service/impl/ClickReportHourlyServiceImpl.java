package org.jeecg.modules.corp.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.jeecg.modules.corp.entity.ClickReportHourly;
import org.jeecg.modules.corp.mapper.ClickReportHourlyMapper;
import org.jeecg.modules.corp.service.IClickReportHourlyService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Map;

/**
 * @Description: 按小时统计点击报表Service实现
 * @Author: jeecg-boot
 * @Date: 2024-05-16
 */
@Service
public class ClickReportHourlyServiceImpl extends ServiceImpl<ClickReportHourlyMapper, ClickReportHourly> implements IClickReportHourlyService {

    @Override
    public List<Map<String, Object>> getClickStatsByDateRange(Map<String, Object> params) {
        return baseMapper.getClickStatsByDateRange(params);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ClickReportHourly add(ClickReportHourly dto) {
        save(dto);
        return dto;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ClickReportHourly edit(ClickReportHourly dto) {
        updateById(dto);
        return dto;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteById(String id) {
        baseMapper.deleteById(id);
    }

    @Override
    public IPage<ClickReportHourly> findPage(IPage<ClickReportHourly> page, ClickReportHourly dto) {
        return baseMapper.selectPage(page, null);
    }

    @Override
    public Integer getTodayClickCount(Integer tenantId) {
        return baseMapper.getTodayClickCount(tenantId);
    }
}
