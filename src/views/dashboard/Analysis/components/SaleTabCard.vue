<template>
  <a-card :loading="loading" :bordered="false" :body-style="{ padding: '0' }">
    <div class="salesCard">
      <a-tabs default-active-key="1" size="large" :tab-bar-style="{ marginBottom: '24px', paddingLeft: '16px' }">
        <template #rightExtra>
          <div class="extra-content">
            <div class="extra-wrapper">
              <a-radio-group v-model:value="barDateType" name="radioGroup">
                <a-radio value="day">日</a-radio>
                <a-radio value="month">月</a-radio>
              </a-radio-group>
              <a-range-picker
                v-model:value="selectBarDate"
                :format="barDateType === 'day' ? 'YYYY-MM-DD' : 'YYYY-MM'"
                :style="{ width: '256px' }"
                @change="onChangeBarDate"
                :picker="barDateType === 'month' ? 'month' : null"
              />
              <span class="sum-title">点击量：</span>
              <span class="sum-num">{{new Intl.NumberFormat("default", { useGrouping: true })
              .format(clickCollect)}}</span>
              <a-button
                type="link"
                size="small"
                @click="getTodayClickData"
                style="margin-left: 8px;"
                title="刷新今日点击数"
              >
                <template #icon>
                  <ReloadOutlined />
                </template>
              </a-button>
            </div>
            <!-- 日期查询部分已移除 -->
          </div>
        </template>
        <a-tab-pane tab="环比趋势" key="1">
          <a-row>
            <a-col :xl="16" :lg="12" :md="12" :sm="24" :xs="24">
              <Bar :chartData="barData" :option="{ title: { text: '', textStyle: { fontWeight: 'lighter' } } }" height="40vh" :seriesColor="seriesColor" />
            </a-col>
            <a-col :xl="8" :lg="12" :md="12" :sm="24" :xs="24" v-loading="rankLoading">
              <RankList
                title="合作公司目录"
                :list="rankList"
                :totalRecord="totalNum"
                :activeLine="activeLine"
                :pageSize="rankParams.pageSize"
                @onPageChange="changePageNum"
                @changeRank="getRankDetail"
              >
                <template #subtool>
                  <a-input
                    v-model:value="searchCompanyName"
                    placeholder="请输入公司名称搜索"
                    @input="onCompanyNameInput"
                    style="width:100%;"
                    allowClear
                    />

                </template>
              </RankList>
            </a-col>
          </a-row>
        </a-tab-pane>
      </a-tabs>
    </div>
  </a-card>
</template>

<script lang="ts" setup>
import { ref, computed, onMounted, onUnmounted, reactive, defineEmits } from 'vue';
import dayjs, { Dayjs } from 'dayjs';
import { ReloadOutlined } from '@ant-design/icons-vue';
import Bar from '/@/components/chart/Bar.vue';
import RankList from '/@/components/chart/RankList.vue';
import { useRootSetting } from '/@/hooks/setting/useRootSetting';
import { getCount, getCompanyList, getCompanyDirectory, getTodayClickCount } from '../api';

defineProps({
  loading: {
    type: Boolean,
  },
});

type RangeValue = [Dayjs, Dayjs];
const { getThemeColor } = useRootSetting();
const rankList = ref([]);
const rankLoading = ref(false);
const rankParams = reactive({
  pageNo: 1,
  pageSize: 10,
  name: ''
});
const barParams = reactive({
  id: null,
  queryType: 'day',
  startTime: '',
  endTime: '',
});
const totalNum = ref(0);
const activeLine = ref('');
const selectBarDate = ref<RangeValue>();
const searchCompanyName = ref('')

const barDateType = ref('day');
const clickCollect = ref(0)
const todayClickCount = ref(0)
const companyOptions = ref([])

const barData = ref([]);
const emit = defineEmits(['changeRankData']);
const seriesColor = computed(() => getThemeColor.value);

// 定时器变量
let refreshTimer: NodeJS.Timeout | null = null;

// 获取合作公司目录数据
async function getRankList(type: string) {
  rankLoading.value = true;
  // 使用新的API获取合作公司目录
  const params = {
    pageNo: rankParams.pageNo,
    pageSize: rankParams.pageSize,
    name: rankParams.name
  };
  const data = await getCompanyDirectory(params);
  rankLoading.value = false;

  // 处理返回的数据
  if (data && data.records) {
    // 将数据转换为RankList组件需要的格式
    rankList.value = data.records.map((item: any) => ({
      id: item.id,
      name: item.name
    }));
    totalNum.value = data.total;

    if (type === 'init' || type === "change") {
      if (data.records?.length) {
        if (type === 'init'){
          selectBarDate.value = [dayjs().subtract(12, 'day'), dayjs()];
          barParams.startTime = dayjs().subtract(12, 'day').format('YYYY-MM-DD');
          barParams.endTime = dayjs().format('YYYY-MM-DD');
        }
        barParams.id = data.records[0].id;
        activeLine.value = data.records[0].id;
        emit('changeRankData', data.records[0]);
      }
      getBarData();
    }
  }
}
// 公司名称输入
function onCompanyNameInput(e: Event) {
  const value = (e.target as HTMLInputElement).value;
  // 更新排行榜查询参数中的公司名称
  rankParams.name = value || '';
  rankParams.pageNo = 1;
  // 查询排行榜时需要传递日期参数（保留当前的日期参数）
  getRankList('search');
}

// 切换页面
function changePageNum(val: number) {
  rankParams.pageNo = val;
  getRankList('page');
}

// 日期相关函数已移除

// 切换柱状图日期选择范围
function onChangeBarDate(_val: any, dateString: string[]) {
  if (dateString && Array.isArray(dateString) && dateString.length === 2) {
    barParams.startTime = dateString[0];
    barParams.endTime = dateString[1];
  } else {
    barParams.startTime = '';
    barParams.endTime = '';
  }
  getBarData();
}

// 点击排行榜数据获取详情
function getRankDetail(val: any) {
  barParams.id = val.id; // 修复了这里的错误，确保id更新
  activeLine.value = val.id;
  emit('changeRankData', val);
  getBarData();
}

// 获取柱状图数据
async function getBarData() {
  barParams.queryType = barDateType.value;
  const data = await getCount(barParams);
  clickCollect.value = data.clickCollect;
  barData.value = data.clickList?.map((ele: any) => ({
    name: ele.meent,
    value: ele.clickSum,
  }));
}


// 获取所有合作公司
const getCompanyOptions = async(params?: any) => {
  const data = await getCompanyList(params);
  companyOptions.value = data && data.map((ele: any) => {
    return {
      label: ele.name,
      value: ele.id,
      name: ele.name
    }
  })
}

// 获取今日点击数
const getTodayClickData = async() => {
  try {
    const data = await getTodayClickCount();
    if (data && data.todayClickCount !== undefined) {
      todayClickCount.value = data.todayClickCount;
    }
  } catch (error) {
    console.error('获取今日点击数失败:', error);
    todayClickCount.value = 0;
  }
}

// 刷新间隔配置
const refreshConfig = {
  minInterval: 60, // 最小间隔（秒）
  maxInterval: 66, // 最大间隔（秒）
};

// 生成随机刷新间隔
const getRandomRefreshInterval = () => {
  const min = refreshConfig.minInterval * 1000; // 转换为毫秒
  const max = refreshConfig.maxInterval * 1000; // 转换为毫秒
  const randomInterval = min + Math.floor(Math.random() * (max - min));
  return randomInterval;
}

// 启动定时刷新
const startRefreshTimer = () => {
  const scheduleNextRefresh = () => {
    const interval = getRandomRefreshInterval();
    console.log(`SaleTabCard下次刷新将在 ${interval / 1000} 秒后执行`);

    refreshTimer = setTimeout(() => {
      getTodayClickData();
      // 刷新完成后，安排下一次刷新
      scheduleNextRefresh();
    }, interval);
  };

  // 开始第一次调度
  scheduleNextRefresh();
}

// 清理定时器
const clearRefreshTimer = () => {
  if (refreshTimer) {
    clearTimeout(refreshTimer);
    refreshTimer = null;
    console.log('SaleTabCard定时刷新已停止');
  }
}
onMounted(() => {
  getRankList('init');
  getCompanyOptions();
  getTodayClickData();
  startRefreshTimer(); // 启动定时刷新
});

onUnmounted(() => {
  clearRefreshTimer(); // 清理定时器
});
</script>

<style lang="less" scoped>
:deep(.ant-tabs-extra-content){
  width: calc(100% - 100px);
}
.extra-content {
  display: flex;
  justify-content: space-between;
  .sum-title{
    color: #788296;
    font-size: 14px;
    margin-left: 20px;
  }
  .sum-num{
    color: #1890ff;
    font-size: 18px;
  }
}
.extra-wrapper {
  line-height: 55px;
  padding-right: 24px;
  .extra-item {
    display: inline-block;
    margin-right: 24px;
    a {
      margin-left: 12px;
      padding: 4px 6px;
      border-radius: 4px;
      &.active {
        background: #1890ff;
        color: #fff;
      }
    }
  }
}
</style>
