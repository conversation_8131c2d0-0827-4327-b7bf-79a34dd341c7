import { defHttp } from '/@/utils/http/axios';

enum Api {
  loginfo = '/sys/loginfo',
  visitInfo = '/sys/visitInfo',
  saleInfo = '/produce/whBackgrounConfig/queryByCode',
  rank = '/firmInformation/whFirmInformation/count',
  bar = '/firmInformation/whFirmInformation/getCount',
  ipCount = '/firmInformation/whFirmInformation/getIpCount',
  companyList = '/firmInformation/whFirmInformation/getAllList',
  companyDirectory = '/sys/tenant/getCompanyDirectory',
  todayClickCount = '/click-report-hourly/getTodayClickCount',
}
/**
 * 日志统计信息
 * @param params
 */
export const getLoginfo = (params) => defHttp.get({ url: Api.loginfo, params }, { isTransformResponse: false });
/**
 * 访问量信息
 * @param params
 */
export const getVisitInfo = (params) => defHttp.get({ url: Api.visitInfo, params }, { isTransformResponse: false });
/**
 * @description: 获取销售信息
 */

export const queryByCode = (params) => {
  return defHttp.get({ url: Api.saleInfo, params });
};
/**
 * @description: 获取排行榜
 */

export const getRank = (params) => {
  return defHttp.get({ url: Api.rank, params });
};
/**
 * @description: 获取排行榜对应柱状图数据
 */

export const getCount = (params) => {
  return defHttp.get({ url: Api.bar, params });
};
/**
 * @description: 访问量
 */

export const getIpCount = (params) => {
  return defHttp.get({ url: Api.ipCount, params });
};
/**
 * 获取合作公司列表
 * @param params
 */
export const getCompanyList = (params) => {
  return defHttp.get({ url: Api.companyList, params });
};

/**
 * 获取合作公司目录
 * @param params
 */
export const getCompanyDirectory = (params) => {
  return defHttp.get({ url: Api.companyDirectory, params });
};

/**
 * 获取今日点击数统计
 */
export const getTodayClickCount = () => {
  return defHttp.get({ url: Api.todayClickCount });
};
