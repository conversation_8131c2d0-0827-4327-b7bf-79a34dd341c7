import {BasicColumn} from '/@/components/Table';
import {FormSchema} from '/@/components/Table';
import { rules} from '/@/utils/helper/validator';
import { render } from '/@/utils/common/renderUtils';
import { getWeekMonthQuarterYear } from '/@/utils';
import { isTenantIdZero } from '/@/store/modules/permissionUtils';
//列表数据
export const columns: BasicColumn[] = [
  {
    title: '日期',
    align:"center",
    dataIndex: 'createTime',
    resizable: true,
    sorter: true
  },
  {
    title: 'ip地址',
    align:"center",
    dataIndex: 'ipAddress',
    resizable: true,
    sorter: true
   },

   {
    title: '用户名称',
    align:"center",
    dataIndex: 'guestName',
    resizable: true,
    sorter: true
   },
  {
    title: '城市',
    align:"center",
    dataIndex: 'city',
    resizable: true,
    sorter: true
  },

  {
    title: '手机号',
    align:"center",
    dataIndex: 'phoneNumber'
   },
   {
    title: '车牌号',
    align:"center",
    dataIndex: 'licensePlateNumber',
    resizable: true,
    sorter: true
   },
   {
    title: '所有人',
    align:"center",
    dataIndex: 'owner',
    resizable: true,
    sorter: true
   },
   // {
   //  title: '发动机号码',
   //  align:"center",
   //  dataIndex: 'engineNumber'
   // },
   {
    title: '厂牌型号',
    align:"center",
    dataIndex: 'model',
    resizable: true,
    sorter: true
   },
   // {
   //  title: '使用性质',
   //  align:"center",
   //  dataIndex: 'useNature'
   // },
   // {
   //  title: '车辆类型',
   //  align:"center",
   //  dataIndex: 'vehicleType'
   // },
   // {
   //  title: '出生日期',
   //  align:"center",
   //  dataIndex: 'birthDate'
   // },
   {
    title: '车架号',
    align:"center",
    dataIndex: 'vinCode',
    resizable: true,
    sorter: true
   },
   {
    title: '性别',
    align:"center",
    dataIndex: 'sex',
    resizable: true,
    sorter: true
   },
   // {
   //  title: '民族',
   //  align:"center",
   //  dataIndex: 'ethnicity'
   // },
];
if (isTenantIdZero()) {
  columns.push({
    title: '主体名称',
    align: "center",
    dataIndex: 'tenantName',
    resizable: true,
    sorter: true
  });
}
//查询数据
export const searchFormSchema: FormSchema[] = [
  {
    label: '车牌/所有人',
    field: 'keyword',
    component: 'Input',
    componentProps: {
      placeholder: '请输入车牌号或所有人',
    },
    //colProps: {span: 6},
  },
  {
    label: '所属公司',
    field: 'tenantId',
    component: 'TenantSelect',
    componentProps: {
      placeholder: '请输入公司名称',
      showSearch: true,
      allowClear: true,
      api: '/sys/tenant/getTenantList'
    },
    ifShow: () => isTenantIdZero(),
},
{
  label: '日期范围',
  field: 'dateRange',
  component: 'RangePicker',
  componentProps: {
    format: 'YYYY-MM-DD', // 格式化日期
    valueFormat: 'YYYY-MM-DD', // 提交的值格式
    placeholder: ['开始日期', '结束日期'],
  },
},
];
//表单数据
export const formSchema: FormSchema[] = [

  {
    label: '车牌号',
    field: 'licensePlateNumber',
    component: 'Input',
  },
  {
    label: '手机号',
    field: 'phoneNumber',
    component: 'Input',
  },
  {
    label: '所有人',
    field: 'owner',
    component: 'Input',
  },
  {
    label: '发动机号码',
    field: 'engineNumber',
    component: 'Input',
  },
  {
    label: '品牌型号',
    field: 'model',
    component: 'Input',
  },
  // {
  //   label: '使用性质',
  //   field: 'useNature',
  //   component: 'Input',
  // },
  {
    label: '车辆类型',
    field: 'vehicleType',
    component: 'Input',
  },
  {
    label: '身份证号码',
    field: 'idNumber',
    component: 'Input',
  },
  {
    label: '出生日期',
    field: 'birthDate',
    component: 'Input',
  },
  {
    label: '车辆识别代码',
    field: 'vinCode',
    component: 'Input',
  },
  {
    label: '性别',
    field: 'sex',
    component: 'Input',
  },
  // {
  //   label: '民族',
  //   field: 'ethnicity',
  //   component: 'Input',
  // },
  {
    label: '所属公司',
    field: 'tenantId',
    component: 'TenantSelect',
    componentProps: {
      placeholder: '请输入公司名称',
      showSearch: true,
      allowClear: true,
      api: '/sys/tenant/getTenantList'
    },
    ifShow: () => isTenantIdZero(),
},
	// TODO 主键隐藏字段，目前写死为ID
	{
	  label: '',
	  field: 'id',
	  component: 'Input',
	  show: false
	},
];

// 高级查询数据
export const superQuerySchema = {
  licensePlateNumber: {title: '车牌号',order: 1,view: 'text', type: 'string',},
  owner: {title: '所有人',order: 2,view: 'text', type: 'string',},
  engineNumber: {title: '发动机号码',order: 3,view: 'text', type: 'string',},
  model: {title: '品牌型号',order: 4,view: 'text', type: 'string',},
  useNature: {title: '使用性质',order: 5,view: 'text', type: 'string',},
  vehicleType: {title: '车辆类型',order: 6,view: 'text', type: 'string',},
  idNumber: {title: '身份证号码',order: 7,view: 'text', type: 'string',},
  birthDate: {title: '出生日期',order: 8,view: 'text', type: 'string',},
  vinCode: {title: '车辆识别代码',order: 9,view: 'text', type: 'string',},
  sex: {title: '性别',order: 10,view: 'text', type: 'string',},
  ethnicity: {title: '民族',order: 11,view: 'text', type: 'string',},
};

/**
* 流程表单调用这个方法获取formSchema
* @param param
*/
export function getBpmFormSchema(_formData): FormSchema[]{
  // 默认和原始表单保持一致 如果流程中配置了权限数据，这里需要单独处理formSchema
  return formSchema;
}
