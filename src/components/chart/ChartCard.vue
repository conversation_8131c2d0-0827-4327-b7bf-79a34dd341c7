<template>
  <a-card :loading="loading" :body-style="{ padding: '20px 24px 8px' }" :bordered="false">
    <div class="chart-card-header">
      <div class="meta">
        <div class="title-row">
          <span class="chart-card-title">{{ title }}</span>
          <span class="chart-card-title-action">
            <slot name="titleAction"></slot>
          </span>
        </div>
        <span class="chart-card-action">
          <slot name="action"></slot>
        </span>
      </div>
      <div class="total">
        <a-tooltip :title="tooltipText" placement="top">
          <span class="number-with-info">
            {{ formattedTotal }}
            <info-circle-outlined class="info-icon" />
          </span>
        </a-tooltip>
      </div>
    </div>
    <div class="chart-card-content">
      <div class="content-fix">
        <slot></slot>
      </div>
    </div>
    <div class="chart-card-footer">
      <div class="field">
        <slot name="footer"></slot>
      </div>
    </div>
  </a-card>
</template>

<script lang="ts" setup>
  import { computed } from 'vue';
  import { InfoCircleOutlined } from '@ant-design/icons-vue';
  import { formatLargeNumber, formatNumberWithCommas } from '/@/utils/numberFormat';

  const props = defineProps({
    title: {
      type: String,
      default: '',
    },
    total: {
      type: [Number, String],
      default: '',
    },
    loading: {
      type: Boolean,
      default: false,
    },
  });

  // 格式化数字显示
  const formattedTotal = computed(() => {
    if (typeof props.total === 'string') {
      // 如果是百分比或其他特殊格式，直接返回
      if (props.total.includes('%')) return props.total;

      // 尝试将字符串转换为数字
      const num = Number(props.total);
      if (isNaN(num)) return props.total;
      return formatLargeNumber(num);
    } else if (typeof props.total === 'number') {
      return formatLargeNumber(props.total);
    }
    return props.total;
  });

  // 悬停提示文本
  const tooltipText = computed(() => {
    if (typeof props.total === 'string') {
      if (props.total.includes('%')) return props.total;

      const num = Number(props.total);
      if (isNaN(num)) return props.total;
      return formatNumberWithCommas(num);
    } else if (typeof props.total === 'number') {
      return formatNumberWithCommas(props.total);
    }
    return props.total;
  });
</script>

<style lang="less" scoped>
  .chart-card-header {
    position: relative;
    overflow: hidden;
    width: 100%;

    .meta {
      position: relative;
      overflow: hidden;
      width: 100%;
      color: rgba(0, 0, 0, 0.45);
      font-size: 14px;
      line-height: 22px;
    }

    .title-row {
      display: flex;
      align-items: center;
      gap: 8px;
    }

    .chart-card-title-action {
      display: inline-flex;
      align-items: center;
    }
  }

  .chart-card-action {
    cursor: pointer;
    position: absolute;
    top: 0;
    right: 0;
  }

  .chart-card-footer {
    border-top: 1px solid #e8e8e8;
    padding-top: 9px;
    margin-top: 8px;

    > * {
      position: relative;
    }

    .field {
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      margin: 0;
    }
  }

  .chart-card-content {
    margin-bottom: 12px;
    position: relative;
    height: 46px;
    width: 100%;

    .content-fix {
      position: absolute;
      left: 0;
      bottom: 0;
      width: 100%;
    }
  }

  .total {
    overflow: hidden;
    text-overflow: ellipsis;
    word-break: break-all;
    white-space: nowrap;
    margin-top: 4px;
    margin-bottom: 0;
    font-size: 30px;
    line-height: 38px;
    height: 38px;

    .number-with-info {
      position: relative;
      cursor: help;
      border-bottom: 1px dashed rgba(0, 0, 0, 0.45);
      padding-bottom: 2px;

      &:hover {
        color: #1890ff;
      }

      .info-icon {
        font-size: 16px;
        margin-left: 4px;
        color: rgba(0, 0, 0, 0.45);
        position: relative;
        top: -2px;
      }
    }
  }
</style>
