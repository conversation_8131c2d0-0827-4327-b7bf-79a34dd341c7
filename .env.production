# 是否启用mock
VITE_USE_MOCK = true

# 发布路径
VITE_PUBLIC_PATH = /

# 公司和系统配置
VITE_COMPANY_ID = TaiYi
VITE_COMPANY_NAME = 太一企业
VITE_COMPANY_FULL_NAME = 福州太一市企业管理有限公司
VITE_SYSTEM_NAME = 太一系统

# 网站标题
VITE_GLOB_APP_TITLE = 太一企业
VITE_COMPANY_LOGO = taiyi_logo.png
VITE_GLOB_APP_SHORT_NAME = TaiYi
# 旭动
# VITE_GLOB_APP_TITLE = 太一企业
# VITE_COMPANY_LOGO = taiyi_logo.png
# VITE_GLOB_APP_SHORT_NAME = TaiYi

# 登录页版权信息
VITE_GLOB_COPYRIGHT = ©2024 福州太一市企业管理有限公司
VITE_GLOB_ICP = 闽ICP备2024066292号-2

# 是否启用gzip或brotli压缩
# 选项值: gzip | brotli | none
# 如果需要多个可以使用","分隔
VITE_BUILD_COMPRESS = 'gzip'

# 使用压缩时是否删除原始文件，默认为false
VITE_BUILD_COMPRESS_DELETE_ORIGIN_FILE = false

#后台接口父地址(必填),带杠为打包环境
VITE_GLOB_API_URL=/jeecg-boot
#VITE_GLOB_API_URL=/jeecgboot

#后台接口全路径地址(必填)
#VITE_GLOB_DOMAIN_URL=http://**********:8080/jeecg-boot
#VITE_GLOB_DOMAIN_URL=http://127.0.0.1:8080/jeecg-boot
#VITE_GLOB_DOMAIN_URL=http://************:8080/jeecg-boot
#VITE_GLOB_DOMAIN_URL=https://www.lunarforge.cn/jeecg-boot
VITE_GLOB_DOMAIN_URL=http://************:8089/jeecg-boot
#VITE_GLOB_DOMAIN_URL=https://www.xdinnovation.cn/jeecg-boot

# 接口父路径前缀
VITE_GLOB_API_URL_PREFIX=
